CXX = g++
CXXFLAGS = -std=c++17 -O3 -Wall -Wextra
LIBS = -lSDL2 -lm -lpthread
TARGET = ubuntu-piano
SOURCE = piano.cpp

# Default target
all: $(TARGET)

# Build the piano application
$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE) $(LIBS)

# Install dependencies (Ubuntu/Debian)
install-deps:
	sudo apt update
	sudo apt install -y libsdl2-dev build-essential

# Clean build files
clean:
	rm -f $(TARGET)

# Run the application
run: $(TARGET)
	./$(TARGET)

# Install system-wide (optional)
install: $(TARGET)
	sudo cp $(TARGET) /usr/local/bin/
	sudo chmod +x /usr/local/bin/$(TARGET)

# Uninstall
uninstall:
	sudo rm -f /usr/local/bin/$(TARGET)

.PHONY: all clean run install uninstall install-deps