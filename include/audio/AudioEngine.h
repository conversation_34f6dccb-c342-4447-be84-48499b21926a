#pragma once

#include <QObject>
#include <memory>
#include <vector>
#include <atomic>
#include <mutex>

namespace VirtuPiano {
namespace Audio {

class AudioDevice;
class SynthEngine;
class EffectsChain;

/**
 * @brief Audio backend types supported by the engine
 */
enum class AudioBackend {
    Auto,       ///< Automatically select best available backend
    JACK,       ///< JACK Audio Connection Kit
    PulseAudio, ///< PulseAudio
    ALSA        ///< Advanced Linux Sound Architecture
};

/**
 * @brief Audio configuration structure
 */
struct AudioConfig {
    AudioBackend backend{AudioBackend::Auto};
    int sampleRate{44100};
    int bufferSize{512};
    int channels{2};
    std::string deviceName;
};

/**
 * @brief High-performance audio engine for real-time audio processing
 * 
 * This class manages the audio pipeline, including device management,
 * synthesis, effects processing, and output. It's designed for ultra-low
 * latency performance suitable for professional music applications.
 */
class AudioEngine : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Construct the audio engine
     * @param parent Parent QObject
     */
    explicit AudioEngine(QObject* parent = nullptr);
    
    /**
     * @brief Destructor - ensures clean shutdown
     */
    ~AudioEngine() override;

    /**
     * @brief Initialize the audio engine with given configuration
     * @param config Audio configuration
     * @return true if initialization was successful
     */
    bool initialize(const AudioConfig& config);

    /**
     * @brief Start audio processing
     * @return true if started successfully
     */
    bool start();

    /**
     * @brief Stop audio processing
     */
    void stop();

    /**
     * @brief Check if the engine is running
     * @return true if running
     */
    bool isRunning() const;

    /**
     * @brief Get current audio configuration
     * @return Current configuration
     */
    const AudioConfig& config() const;

    /**
     * @brief Get available audio devices
     * @return Vector of device names
     */
    std::vector<std::string> getAvailableDevices() const;

    /**
     * @brief Get current audio latency in milliseconds
     * @return Latency in ms
     */
    double getLatency() const;

    /**
     * @brief Get synthesis engine
     * @return Reference to synth engine
     */
    SynthEngine& synthEngine() const;

    /**
     * @brief Get effects chain
     * @return Reference to effects chain
     */
    EffectsChain& effectsChain() const;

public slots:
    /**
     * @brief Play a note
     * @param note MIDI note number (0-127)
     * @param velocity Note velocity (0.0-1.0)
     */
    void playNote(int note, float velocity);

    /**
     * @brief Stop a note
     * @param note MIDI note number (0-127)
     */
    void stopNote(int note);

    /**
     * @brief Stop all notes
     */
    void stopAllNotes();

    /**
     * @brief Set master volume
     * @param volume Volume level (0.0-1.0)
     */
    void setMasterVolume(float volume);

signals:
    /**
     * @brief Emitted when audio engine starts
     */
    void started();

    /**
     * @brief Emitted when audio engine stops
     */
    void stopped();

    /**
     * @brief Emitted when an error occurs
     * @param error Error message
     */
    void errorOccurred(const QString& error);

    /**
     * @brief Emitted when latency changes
     * @param latency New latency in milliseconds
     */
    void latencyChanged(double latency);

private:
    /**
     * @brief Audio processing callback (called from audio thread)
     * @param outputBuffer Output audio buffer
     * @param frameCount Number of frames to process
     */
    void processAudio(float* outputBuffer, int frameCount);

    /**
     * @brief Initialize the selected audio backend
     * @return true if successful
     */
    bool initializeBackend();

    /**
     * @brief Cleanup audio resources
     */
    void cleanup();

private:
    AudioConfig m_config;
    std::unique_ptr<AudioDevice> m_audioDevice;
    std::unique_ptr<SynthEngine> m_synthEngine;
    std::unique_ptr<EffectsChain> m_effectsChain;
    
    std::atomic<bool> m_running{false};
    std::atomic<float> m_masterVolume{0.8f};
    
    mutable std::mutex m_configMutex;
    
    // Audio processing thread safety
    static constexpr int MAX_POLYPHONY = 128;
};

} // namespace Audio
} // namespace VirtuPiano
