#pragma once

#include <QApplication>
#include <memory>

namespace VirtuPiano {
namespace Core {

class AudioEngine;
class ConfigManager;

/**
 * @brief Main application class that orchestrates the entire VirtuPiano Pro application
 * 
 * This class follows the singleton pattern and manages the lifecycle of all major
 * components including the audio engine, configuration, and main window.
 */
class Application : public QApplication {
    Q_OBJECT

public:
    /**
     * @brief Construct the application
     * @param argc Command line argument count
     * @param argv Command line arguments
     */
    Application(int& argc, char** argv);
    
    /**
     * @brief Destructor - ensures clean shutdown
     */
    ~Application() override;

    /**
     * @brief Get the singleton instance
     * @return Reference to the application instance
     */
    static Application& instance();

    /**
     * @brief Initialize the application
     * @return true if initialization was successful
     */
    bool initialize();

    /**
     * @brief Run the application main loop
     * @return Exit code
     */
    int run();

    /**
     * @brief Get the audio engine
     * @return Reference to the audio engine
     */
    AudioEngine& audioEngine() const;

    /**
     * @brief Get the configuration manager
     * @return Reference to the configuration manager
     */
    ConfigManager& configManager() const;

public slots:
    /**
     * @brief Handle application shutdown
     */
    void shutdown();

private slots:
    /**
     * @brief Handle last window closed signal
     */
    void onLastWindowClosed();

private:
    /**
     * @brief Setup application properties and metadata
     */
    void setupApplicationProperties();

    /**
     * @brief Initialize logging system
     */
    void initializeLogging();

    /**
     * @brief Load application configuration
     */
    bool loadConfiguration();

    /**
     * @brief Initialize the audio engine
     */
    bool initializeAudioEngine();

    /**
     * @brief Create and show the main window
     */
    bool createMainWindow();

private:
    static Application* s_instance;
    
    std::unique_ptr<AudioEngine> m_audioEngine;
    std::unique_ptr<ConfigManager> m_configManager;
    
    bool m_initialized{false};
};

} // namespace Core
} // namespace VirtuPiano
