cmake_minimum_required(VERSION 3.20)

project(VirtuPianoPro 
    VERSION 1.0.0
    DESCRIPTION "Professional Virtual Piano for Ubuntu Linux"
    LANGUAGES CXX
)

# Set C++ standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set default build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    add_compile_options(
        -Wall -Wextra -Wpedantic
        -Wno-unused-parameter
        -ffast-math
        -march=native
    )
    
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        add_compile_options(-g -O0 -fsanitize=address)
        add_link_options(-fsanitize=address)
    else()
        add_compile_options(-O3 -DNDEBUG)
    endif()
endif()

# Find required packages
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Multimedia)
find_package(PkgConfig REQUIRED)

# Find audio libraries
pkg_check_modules(JACK jack)
pkg_check_modules(PULSE libpulse)

if(NOT JACK_FOUND AND NOT PULSE_FOUND)
    message(FATAL_ERROR "Neither JACK nor PulseAudio development libraries found")
endif()

# Include Conan dependencies
if(EXISTS ${CMAKE_BINARY_DIR}/conanbuildinfo.cmake)
    include(${CMAKE_BINARY_DIR}/conanbuildinfo.cmake)
    conan_basic_setup()
endif()

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/include)

# Add subdirectories
add_subdirectory(src)

# Enable testing
enable_testing()
add_subdirectory(tests)

# Installation
install(TARGETS virtupiano-pro
    RUNTIME DESTINATION bin
)

install(DIRECTORY resources/
    DESTINATION share/virtupiano-pro/resources
)

# Create desktop entry
configure_file(
    ${CMAKE_SOURCE_DIR}/resources/virtupiano-pro.desktop.in
    ${CMAKE_BINARY_DIR}/virtupiano-pro.desktop
    @ONLY
)

install(FILES ${CMAKE_BINARY_DIR}/virtupiano-pro.desktop
    DESTINATION share/applications
)

# Package configuration
set(CPACK_PACKAGE_NAME "virtupiano-pro")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "VirtuPiano Team")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")

set(CPACK_GENERATOR "DEB")
set(CPACK_DEBIAN_PACKAGE_DEPENDS "qt6-base-dev, libjack-jackd2-0 | libpulse0")
set(CPACK_DEBIAN_PACKAGE_SECTION "multimedia")
set(CPACK_DEBIAN_PACKAGE_PRIORITY "optional")

include(CPack)
