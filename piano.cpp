#include <SDL2/SDL.h>
#include <SDL2/SDL_audio.h>
#include <iostream>
#include <cmath>
#include <map>
#include <vector>
#include <thread>
#include <atomic>

const int SAMPLE_RATE = 44100;
const int AMPLITUDE = 28000;
const int BUFFER_SIZE = 512;

struct Note {
    double frequency;
    double phase;
    double amplitude;
    bool active;
    double decay;
    
    Note() : frequency(0), phase(0), amplitude(0), active(false), decay(1.0) {}
};

class VirtualPiano {
private:
    SDL_AudioDeviceID audioDevice;
    std::map<SDL_Scancode, double> keyFrequencies;
    std::vector<Note> activeNotes;
    std::atomic<bool> running;
    
    // Note frequencies (A4 = 440Hz)
    void initializeKeyMappings() {
        // White keys (C major scale)
        keyFrequencies[SDL_SCANCODE_A] = 261.63; // C4
        keyFrequencies[SDL_SCANCODE_S] = 293.66; // D4
        keyFrequencies[SDL_SCANCODE_D] = 329.63; // E4
        keyFrequencies[SDL_SCANCODE_F] = 349.23; // F4
        keyFrequencies[SDL_SCANCODE_G] = 392.00; // G4
        keyFrequencies[SDL_SCANCODE_H] = 440.00; // A4
        keyFrequencies[SDL_SCANCODE_J] = 493.88; // B4
        keyFrequencies[SDL_SCANCODE_K] = 523.25; // C5
        keyFrequencies[SDL_SCANCODE_L] = 587.33; // D5
        
        // Black keys (sharps/flats)
        keyFrequencies[SDL_SCANCODE_W] = 277.18; // C#4
        keyFrequencies[SDL_SCANCODE_E] = 311.13; // D#4
        keyFrequencies[SDL_SCANCODE_T] = 369.99; // F#4
        keyFrequencies[SDL_SCANCODE_Y] = 415.30; // G#4
        keyFrequencies[SDL_SCANCODE_U] = 466.16; // A#4
        keyFrequencies[SDL_SCANCODE_O] = 554.37; // C#5
        keyFrequencies[SDL_SCANCODE_P] = 622.25; // D#5
        
        // Lower octave
        keyFrequencies[SDL_SCANCODE_Z] = 130.81; // C3
        keyFrequencies[SDL_SCANCODE_X] = 146.83; // D3
        keyFrequencies[SDL_SCANCODE_C] = 164.81; // E3
        keyFrequencies[SDL_SCANCODE_V] = 174.61; // F3
        keyFrequencies[SDL_SCANCODE_B] = 196.00; // G3
        keyFrequencies[SDL_SCANCODE_N] = 220.00; // A3
        keyFrequencies[SDL_SCANCODE_M] = 246.94; // B3
    }
    
    static void audioCallback(void* userdata, Uint8* stream, int len) {
        VirtualPiano* piano = static_cast<VirtualPiano*>(userdata);
        piano->generateAudio(reinterpret_cast<Sint16*>(stream), len / 2);
    }
    
    void generateAudio(Sint16* stream, int length) {
        for (int i = 0; i < length; i++) {
            double sample = 0.0;
            
            // Mix all active notes
            for (auto& note : activeNotes) {
                if (note.active && note.amplitude > 0.001) {
                    // Generate sine wave with envelope
                    double wave = sin(note.phase) * note.amplitude * note.decay;
                    sample += wave;
                    
                    // Update phase
                    note.phase += 2.0 * M_PI * note.frequency / SAMPLE_RATE;
                    if (note.phase > 2.0 * M_PI) {
                        note.phase -= 2.0 * M_PI;
                    }
                    
                    // Apply decay for natural sound
                    note.decay *= 0.9999;
                    if (note.decay < 0.001) {
                        note.active = false;
                    }
                }
            }
            
            // Clamp and convert to 16-bit
            if (sample > 1.0) sample = 1.0;
            if (sample < -1.0) sample = -1.0;
            stream[i] = static_cast<Sint16>(sample * AMPLITUDE);
        }
        
        // Remove inactive notes
        activeNotes.erase(
            std::remove_if(activeNotes.begin(), activeNotes.end(),
                [](const Note& n) { return !n.active; }),
            activeNotes.end()
        );
    }
    
public:
    VirtualPiano() : running(true) {
        initializeKeyMappings();
        activeNotes.reserve(32); // Pre-allocate for performance
    }
    
    bool initialize() {
        if (SDL_Init(SDL_INIT_AUDIO) < 0) {
            std::cerr << "SDL initialization failed: " << SDL_GetError() << std::endl;
            return false;
        }
        
        SDL_AudioSpec desiredSpec, obtainedSpec;
        desiredSpec.freq = SAMPLE_RATE;
        desiredSpec.format = AUDIO_S16SYS;
        desiredSpec.channels = 1;
        desiredSpec.samples = BUFFER_SIZE;
        desiredSpec.callback = audioCallback;
        desiredSpec.userdata = this;
        
        audioDevice = SDL_OpenAudioDevice(nullptr, 0, &desiredSpec, &obtainedSpec, 0);
        if (audioDevice == 0) {
            std::cerr << "Failed to open audio device: " << SDL_GetError() << std::endl;
            return false;
        }
        
        SDL_PauseAudioDevice(audioDevice, 0); // Start audio
        return true;
    }
    
    void playNote(double frequency) {
        // Find existing note or create new one
        auto it = std::find_if(activeNotes.begin(), activeNotes.end(),
            [frequency](const Note& n) { 
                return !n.active && abs(n.frequency - frequency) < 1.0; 
            });
        
        if (it != activeNotes.end()) {
            it->active = true;
            it->amplitude = 0.3;
            it->decay = 1.0;
            it->phase = 0.0;
        } else {
            Note newNote;
            newNote.frequency = frequency;
            newNote.amplitude = 0.3;
            newNote.active = true;
            newNote.decay = 1.0;
            newNote.phase = 0.0;
            activeNotes.push_back(newNote);
        }
    }
    
    void stopNote(double frequency) {
        for (auto& note : activeNotes) {
            if (note.active && abs(note.frequency - frequency) < 1.0) {
                note.decay *= 0.1; // Quick fade out
            }
        }
    }
    
    void run() {
        SDL_Window* window = SDL_CreateWindow(
            "Ubuntu Virtual Piano - Press keys to play!",
            SDL_WINDOWPOS_CENTERED, SDL_WINDOWPOS_CENTERED,
            800, 200, SDL_WINDOW_SHOWN
        );
        
        if (!window) {
            std::cerr << "Window creation failed: " << SDL_GetError() << std::endl;
            return;
        }
        
        SDL_Renderer* renderer = SDL_CreateRenderer(window, -1, SDL_RENDERER_ACCELERATED);
        if (!renderer) {
            std::cerr << "Renderer creation failed: " << SDL_GetError() << std::endl;
            SDL_DestroyWindow(window);
            return;
        }
        
        std::cout << "Ubuntu Virtual Piano Started!" << std::endl;
        std::cout << "Key mappings:" << std::endl;
        std::cout << "White keys: A S D F G H J K L (C4-D5)" << std::endl;
        std::cout << "Black keys: W E T Y U O P (sharps)" << std::endl;
        std::cout << "Lower octave: Z X C V B N M (C3-B3)" << std::endl;
        std::cout << "Press ESC to quit" << std::endl;
        
        SDL_Event event;
        std::map<SDL_Scancode, bool> keyStates;
        
        while (running) {
            while (SDL_PollEvent(&event)) {
                switch (event.type) {
                    case SDL_QUIT:
                        running = false;
                        break;
                        
                    case SDL_KEYDOWN:
                        if (event.key.keysym.scancode == SDL_SCANCODE_ESCAPE) {
                            running = false;
                            break;
                        }
                        
                        if (keyFrequencies.count(event.key.keysym.scancode) && 
                            !keyStates[event.key.keysym.scancode]) {
                            keyStates[event.key.keysym.scancode] = true;
                            playNote(keyFrequencies[event.key.keysym.scancode]);
                        }
                        break;
                        
                    case SDL_KEYUP:
                        if (keyFrequencies.count(event.key.keysym.scancode)) {
                            keyStates[event.key.keysym.scancode] = false;
                            stopNote(keyFrequencies[event.key.keysym.scancode]);
                        }
                        break;
                }
            }
            
            // Clear screen
            SDL_SetRenderDrawColor(renderer, 30, 30, 30, 255);
            SDL_RenderClear(renderer);
            
            // Draw simple piano visualization
            int keyWidth = 80;
            int whiteKeyHeight = 120;
            int blackKeyHeight = 80;
            
            // Draw white keys
            SDL_SetRenderDrawColor(renderer, 255, 255, 255, 255);
            for (int i = 0; i < 9; i++) {
                SDL_Rect rect = {i * keyWidth + 10, 40, keyWidth - 2, whiteKeyHeight};
                SDL_RenderFillRect(renderer, &rect);
                SDL_SetRenderDrawColor(renderer, 0, 0, 0, 255);
                SDL_RenderDrawRect(renderer, &rect);
                SDL_SetRenderDrawColor(renderer, 255, 255, 255, 255);
            }
            
            // Draw black keys
            SDL_SetRenderDrawColor(renderer, 0, 0, 0, 255);
            int blackKeyPositions[] = {0, 1, 3, 4, 5, 7}; // Skip positions 2 and 6
            for (int pos : blackKeyPositions) {
                if (pos < 8) {
                    SDL_Rect rect = {pos * keyWidth + 60, 40, keyWidth/2, blackKeyHeight};
                    SDL_RenderFillRect(renderer, &rect);
                }
            }
            
            SDL_RenderPresent(renderer);
            SDL_Delay(16); // ~60 FPS
        }
        
        SDL_DestroyRenderer(renderer);
        SDL_DestroyWindow(window);
    }
    
    ~VirtualPiano() {
        if (audioDevice != 0) {
            SDL_CloseAudioDevice(audioDevice);
        }
        SDL_Quit();
    }
};

int main() {
    VirtualPiano piano;
    
    if (!piano.initialize()) {
        return -1;
    }
    
    piano.run();
    return 0;
}